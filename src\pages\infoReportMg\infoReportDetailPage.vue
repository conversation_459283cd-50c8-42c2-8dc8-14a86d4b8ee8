<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "产品详情",
    "enablePullDownRefresh": true,
    "backgroundColor": "#f0f3f8"
  }
}
</route>

<script lang="ts" setup>
import type { ReportGoodsLoadV2Res } from '@/service/infoReportApi'
import { storeToRefs } from 'pinia'
import FTitleLabel from '@/components/f-title-label.vue'
import { reportGoodsLoadV2Api } from '@/service/infoReportApi'
import { updateUserPhoneApi } from '@/service/systemApi'
import { infoReportDetailStore } from '@/store/infoReportDetailStore'
import { useUserStore } from '@/store/user'

const useInfoReportDetailStore = infoReportDetailStore()
const userStore = useUserStore()
const { userId, barCodeCardNum, barCodeCardPassword, phone } = storeToRefs(userStore)
const data = ref<ReportGoodsLoadV2Res['data']>()
const goodsId = ref<number>(0)
const barCode = ref<string>('')
const isShowKeyModal = ref(false)

function getDetailById() {
  reportGoodsLoadV2Api({
    goodsId: goodsId.value,
  }).then((res) => {
    data.value = res.data
    uni.stopPullDownRefresh()
  })
}

function getDetailByCode() {
  let code = barCode.value

  // 如果是13位字符串，则首位添加一个 "0"
  if (code && code.length === 13) {
    code = `0${code}`
  }
  reportGoodsLoadV2Api({
    barCode: code,
    userId: userId.value,
  }).then((res) => {
    data.value = res.data
    goodsId.value = res.data.goodsId
  })
}

function toModifyPage() {
  // 将当前数据存入 store
  if (data.value) {
    useInfoReportDetailStore.setReportData(data.value)
  }

  uni.navigateTo({
    url: `/pages/infoReportMg/infoReportDetailFromPage?goodsId=${goodsId.value}`,
  })
}

// TODO 添加通报失败提示
// TODO 添加错误密码按钮

function savePasswordAndReport() {
  if (barCodeCardNum.value === '' || barCodeCardPassword.value === '') {
    uni.showToast({
      title: '请输入条码卡号和密码',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 先更新用户信息
  updateUserPhoneApi({
    barCodeCardNum: barCodeCardNum.value,
    barCodeCardPassword: barCodeCardPassword.value,
    phone: phone.value,
    userId: userId.value,
  }).then(() => {
    userStore.setUpdatePasswordTime()
  }).finally(() => {
    isShowKeyModal.value = false
  })
}

onPullDownRefresh(() => {
  getDetailById()
})

onShow(() => {
  if (goodsId.value) {
    getDetailById()
  }
  else if (barCode.value !== '') {
    getDetailByCode()
  }
})

onLoad((options) => {
  if (options.goodsId) {
    goodsId.value = Number(options.goodsId)
    // goodsId.value = 936
  }
  if (options.barCode) {
    barCode.value = options.barCode
  }
})
// TODO 填写全的，未测试
</script>

<template>
  <view class="p-3 space-y-2">
    <view class="rounded-md bg-white p-4">
      <scroll-view
        v-if="data?.imageList.length > 0" :scroll-x="true" :show-scrollbar="false" class="mb-3 w-full"
        @touchmove.stop @scroll.stop
      >
        <view class="w-max flex space-x-2">
          <image
            v-for="(item, index) in data.imageList" :key="index" :src="item.imageUrl" mode="aspectFit"
            class="f-img-item shrink-0 rounded"
          />
        </view>
      </scroll-view>
      <view class="mb-4 mt-4 text-xl font-bold leading-4">
        {{ data?.goodsName }}
      </view>
      <view class="mt-2 flex items-center text-sm leading-4">
        <view class="shrink-0 text-right text-gray" style="width: 150rpx;">
          公开/保密：
        </view>
        <view
          class="o-tag border rounded border-solid"
          :class="data?.isPrivary ? 'text-red-500 border-red bg-red-50' : 'bg-primary/10 text-primary border-primary'"
        >
          {{ data?.isPrivary ? '保密' : '公开' }}
        </view>
      </view>
    </view>
    <view class="rounded-md bg-white p-4">
      <view class="text-lg text-gray-600 font-bold">
        产品身份信息
      </view>
      <view class="mt-2 flex items-center text-sm leading-4">
        <view class="shrink-0 text-right text-gray" style="width: 150rpx;">
          条码类型：
        </view>
        <view class="o-tag border border-primary rounded border-solid bg-primary/10 text-primary">
          {{ data?.barType }}
        </view>
      </view>
      <FTitleLabel title="条码" :content="data?.barCode" />
      <FTitleLabel title="通用名" :content="data?.commonName" />
      <FTitleLabel v-if="data?.isPrivary != null" title="是否保密">
        {{ data?.isPrivary ? '是' : '否' }}
      </FTitleLabel>
      <FTitleLabel title="品牌" :content="data?.brandName" />
      <FTitleLabel title="产品特征" :content="data?.productFeatures" />
      <FTitleLabel v-if="data?.netContent || data?.netContentUnit" title="净含量">
        {{ data?.netContent }}{{ data?.netContentUnit }}
      </FTitleLabel>
      <FTitleLabel title="规格" :content="data?.spec" />
      <FTitleLabel title="GCP分类" :content="data?.gpcTypeName" />
      <FTitleLabel title="产品描述" :content="data?.goodsDescription" />
      <FTitleLabel title="产品状态" :content="data?.goodsType" />
    </view>
    <view
      v-if="data?.marketDate != null && data?.companyPrice != null && data?.currency != null"
      class="rounded-md bg-white p-4"
    >
      <view class="text-lg text-gray-600 font-bold">
        流通信息
      </view>
      <FTitleLabel title="(预计)上市时间" :content="data?.marketDate" />
      <FTitleLabel title="企业定价" :content="`${data?.companyPrice || ''}${data?.currency || ''}`" />
    </view>
    <view
      v-if="data?.standardList?.length > 0 && data?.standardList[0].executeStandard !== '' && data?.standardList[0].executeStandard != null"
      class="rounded-md bg-white p-4"
    >
      <view class="text-lg text-gray-600 font-bold">
        质量信息
      </view>
      <view class="mt-2 flex text-sm leading-4">
        <view class="shrink-0 text-right text-gray" style="width: 150rpx;">
          产品执行标准：
        </view>
        <view>
          <view v-for="(standardItem, index) in data?.standardList" :key="index">
            {{ `${standardItem.executeStandard} ${standardItem.standardNumber}-${standardItem.executeYear}` }}
          </view>
        </view>
      </view>
    </view>
  </view>
  <view class="py-12" />
  <view class="fixed right-0 box-border w-full p-3" style="bottom:calc(env(safe-area-inset-bottom) + 10rpx)">
    <view class="o-tabbar-shadow box-border w-full rounded-lg bg-white px-3 py-4">
      <FTitleLabel
        title="通报状态" :content="data?.syncAddStateName"
        :content-class="data?.syncAddState ? 'text-emerald-500' : 'text-red-500'"
      />
      <FTitleLabel
        title="微信共享" :content="data?.syncStateName"
        :content-class="data?.syncState ? 'text-emerald-500' : 'text-red-500'"
      />
      <FTitleLabel v-if="data?.syncAddMsg || data?.syncMsg" title="通报失败" content-class="text-red-500">
        <view> {{ data?.syncAddMsg }} </view>
        <view>{{ data?.syncMsg }}</view>
      </FTitleLabel>
      <view
        class="o-bg-primary o-shadow-blue mt-4 flex items-center justify-center rd-2 px-6 py-2 text-sm text-white"
        @click="toModifyPage"
      >
        修改/重新通报
      </view>
    </view>
    <up-modal
      :show="isShowKeyModal" :show-cancel-button="true" title="条码成员身份校验" width="650rpx" confirm-text="提交"
      @confirm="savePasswordAndReport" @cancel="isShowKeyModal = false"
    >
      <view>
        <view class="f-form-item">
          <view class="f-label">
            <text class="o-form-require pr-1">
              条码卡号
            </text>
          </view>
          <view class="o-form-underline flex-1">
            <input v-model.trim="barCodeCardNum">
          </view>
        </view>
        <view class="f-form-item">
          <view class="f-label">
            <text class="o-form-require pr-1">
              条码卡号
            </text>
          </view>
          <view class="o-form-underline flex-1">
            <input v-model.trim="barCodeCardPassword">
          </view>
        </view>
      </view>
    </up-modal>
  </view>
</template>

<style lang="scss" scoped>
.f-img-list {
  touch-action: pan-x;
  -webkit-overflow-scrolling: touch;
  /* 增加iOS滚动流畅度 */
  scrollbar-width: none;
  /* Firefox */
}

.f-img-list::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari */
}

.f-img-item {
  $w: 244rpx;
  width: $w;
  height: $w;
  border: 1px solid #ccc;
}
</style>
